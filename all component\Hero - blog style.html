<!-- Nordace Article Section -->
<section class="pb-0 pt-16 lg:pt-24">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full mx-auto">
            
            <!-- Article Content -->
            <div class="text-left">
                <!-- Main heading -->
                <h1 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-8 lg:mb-12 leading-tight xl:leading-tight">
                    {{article_heading:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. }}
                </h1>
                
                <!-- Author info -->
                <div class="flex items-center gap-4 mb-4 lg:mb-5">
                    <!-- Author avatar -->
                    <img src="{{author_avatar_url:https://picsum.photos/50/50?random=11}}" 
                         class="w-12 h-12 rounded-full object-cover">
                    
                    <!-- Author details -->
                    <div>
                        <p class="text-sm font-medium text-gray-900">
                            By {{author_name:Julia M.}}
                        </p>
                        <p class="text-sm text-gray-600">
                            Last Updated <span id="date-output">Jun 1, 2025</span>
                        </p>
                    </div>
                </div>
                
                <!-- Quote section -->
                <div class="border-l-4 border-gray-900 pl-6 mb-8 py-3 lg:mb-10 bg-orange-50">
                    <p class="text-base lg:text-lg text-gray-700 italic leading-relaxed">
                        {{article_quote:Lorem ipsum dolor sit amet, consectetur adipiscing elit}}
                    </p>
                </div>
                
                <!-- TLDR section -->
                <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                    <span class="font-bold text-gray-900">{{tldr_label:TLDR:}} </span> 
                    <span>{{tldr_content:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}} </span>
                    <span class="text-xl">{{tldr_emoji:🏷️}}</span>
                </p>
            </div>
            
        </div>
    </div>
</section>
<!-- Hidden Script -->
<script>
            function formatDate(date) {
                const monthNames = ["Jan", "Feb", "Mar", "Apr", "May", "Jun", "Jul", "Aug", "Sep", "Oct", "Nov", "Dec"];
                const day = date.getDate();
                const monthIndex = date.getMonth();
                const year = date.getFullYear();
                return `${monthNames[monthIndex]} ${day}, ${year}`;
            }
            function getThreeDaysAgo() {
                const currentDate = new Date();
                currentDate.setDate(currentDate.getDate() - 3);
                return formatDate(currentDate);
            }
            document.getElementById("date-output").innerHTML = getThreeDaysAgo();
</script>
