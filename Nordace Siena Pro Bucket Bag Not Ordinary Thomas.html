<!-- Nordace Product Section -->
<section class="relative py-16 lg:py-24">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full mx-auto">
            
            <!-- Top text content -->
            <div class="text-left sm:text-center mb-12 lg:mb-16">
                <!-- Main heading -->
                <h2 class="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight max-w-4xl mx-auto">
                    {{top_section_heading:Why You’ll Love It in the First 3 Days}}
                </h2>
                
                <!-- Description paragraph 1 -->
                <p class="text-base lg:text-lg text-gray-700 mb-4 lg:mb-6 leading-relaxed max-w-5xl mx-auto">
                    <b>Day 1 – School run & office:</b> Phone goes in the <b>back hidden pocket</b>. Sunglasses slip into the <b>front quick pocket</b>. <PERSON><PERSON> sits upright inside the <b>clear TPU pocket</b> so you can see it. No digging.<br>
                    <b>Day 2 – Groceries & coffee:</b> Pull, drop, go — the <b>drawstring</b> opens/closes in one move.<br>
                    <b>Day 3 – Weekend walk:</b> Wear it <b>crossbody</b> (hands free), clip keys to the <b>retractable key leash</b>, and enjoy the weight. It’s only <b>300 gr / 0.75 lb</b>.
                </p>
                
                <!-- Description paragraph 2 -->
                <p class="text-base lg:text-lg text-gray-700 text-gray-700 leading-relaxed max-w-5xl mx-auto">
                    {{top_section_description2:Choose the calm way to carry—less rummage, fewer delays, more flow in every part of your day.}}
                </p>
            </div>
            
            <!-- Product showcase -->
            <div class="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                    
                <!-- Left column - Product info -->
                <div class="order-1">
                    <!-- Product name -->
                    <h2 class="text-4xl lg:text-5xl xl:text-6xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{overview_heading:Nordace Siena Pro Bucket Bag}}
                    </h2>
                        
                    <!-- Product tagline -->
                    <h3 class="text-2xl lg:text-3xl xl:text-4xl font-bold text-gray-900 leading-tight">
                        {{overview_subtitle:Discover the bucket bag that fits your life}}
                    </h3>
                </div>
                    
                <!-- Right column - Product image -->
                <div class="order-2 flex justify-center">
                    <div class="relative max-w-lg">
                        <img src="{{image_url:https://nordace.com/wp-content/uploads/2022/05/Siena-Pro-Classic-908x800.png}}" 
                             class="object-cover">
                    </div>
                </div>
                    
            </div>
            
        </div>
    </div>
</section>
<!-- Nordace Features Section -->
<section>

    <!-- Feature 1: Pack for adventure (Image left, text right) - Dark stripe background -->
    <div class="w-full py-8 lg:py-12" style="background-color: #f3f3f3;">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature1_image_url:https://picsum.photos/600/800?random=6}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature1_heading:The “Fast-Grab” Blueprint (Make Chaos… Organized)}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Hidden Back Pocket:</b> Slide in your <b>phone</b> for instant reach, screen facing your body.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Front Quick-Access Pocket:</b> For lip balm, hair tie, parking ticket — or your sunglasses when you run inside for 2 minutes.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Clear TPU Pocket + Pen Loops:</b> Wallet stands up, you see it; pens don’t scratch anything.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Dual Mesh Side Pockets:</b> Perfect for a <b>small water bottle</b> or compact umbrella.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-0 sm:mb-6 lg:mb-8 leading-relaxed">
                            <b>Retractable Key Leash:</b> FKeys in 1 pull. No fishing at the bottom.<br>
                            <i>(All real features inside Siena Pro Bucket Bag.)</i>
                        </p>
                        <a href="{{feature1_cta_link:#}}"
                           class="bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 w-full sm:w-fit sm:min-w-60 text-lg hidden sm:inline-block text-center"
                           >
                            {{feature1_cta_text:Step into smart style today}}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 2: Stress-free boarding (Text left, image right) - Light background -->
    <div class="w-full py-8 lg:py-12">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row-reverse gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature2_image_url:https://picsum.photos/600/800?random=7}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature2_heading:Fit Check: Big Phones, Big Win}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            The bag size is <b>15 × 14.5 × 21 cm</b> (Capacity <b>2.5 L</b>). That height (21 cm) easily fits today’s big phones:
                        </p>
                        <ul class="list-disc list-inside text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 pl-3 sm:pl-6 leading-relaxed">
                            <li><b>iPhone 17 Pro Max</b> → <b>163.4 mm x 78 mm</b> (fits with room to spare)</li>
                            <li><b>Samsung Galaxy S25 Ultra</b> → <b>162.8 mm x 79 mm</b> (still shorter than bag height)</li>
                        </ul>    
                        <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                            Add a standard hard <b>sunglasses case</b> + <b>wallet</b>? No problem. The structure keeps items upright so they don’t vanish into the “deep end”.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 3: Less shoulder strain (Image left, text right) - Dark stripe background -->
    <div class="w-full py-8 lg:py-12" style="background-color: #f3f3f3;">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature3_image_url:https://picsum.photos/600/800?random=8}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature3_heading:The Bucket Bag Advantage (vs. Regular Shoulder Bag)}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Drawstring = Smart Security:</b> Tighten in crowds; loosen for instant access — that’s the bucket bag’s superpower.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Vertical Storage = Less Digging:</b> The interior is planned so items stand up and stay visible (clear pocket, loops, side slots).
                        </p>                        
                        <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                            <b>Compact but Useful: 2.5 L</b> capacity, <b>0.34 kg</b> weight — light on the shoulder, strong on function.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 4: Stress-free boarding (Text left, image right) - Light background -->
    <div class="w-full py-8 lg:py-12">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row-reverse gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature4_image_url:https://picsum.photos/600/800?random=7}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature4_heading:Everyday Safety, Upgraded}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>RFID-Blocking Card Holder:</b> Peace of mind when tap-to-pay is everywhere now (contactless is ~<b>50%</b> of U.S. face‑to‑face transactions).
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Hidden Back Pocket:</b> Keep phone/passport against your body.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-0 sm:mb-6 lg:mb-8 leading-relaxed">
                             <b>Water-Resistant Build:</b> Drizzles, splashes, coffee oops — you’re covered.
                        </p>
                        <a href="{{feature4_cta_link:#}}"
                           class="bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 w-full sm:w-fit sm:min-w-60 text-lg hidden sm:inline-block text-center"
                        >
                            {{feature4_cta_text:Find your perfect carry-all bag}}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 5: Less shoulder strain (Image left, text right) - Dark stripe background -->
    <div class="w-full py-8 lg:py-12" style="background-color: #f3f3f3;">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature5_image_url:https://picsum.photos/600/800?random=8}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature5_heading:Carry It Your Way}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                            Wear it <b>crossbody</b> or <b>on the shoulder</b>. The <b>detachable strap</b> comes with a <b>strap card clip</b> so you can attach your phone for truly hands-free days. <b>Woven loops</b> on the strap let you clip sanitizer/AirTag/pouches. Colors: <b>Black–Brown</b> or <b>Beige–Brown</b>.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 6: Stress-free boarding (Text left, image right) - Light background -->
    <div class="w-full py-8 lg:py-12">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row-reverse gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5 lg:-mb-20">
                        <img src="{{feature6_image_url:https://picsum.photos/600/800?random=7}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature6_heading:Real-Life Mini Scenarios (So You Can Picture It)}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Car pickup line:</b> Window down → grab sunglasses from the <b>front pocket</b> in one motion.
                        </p>
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Checkout counter:</b> Wallet is upright in the <b>clear pocket</b> — pay and move.
                        </p> 
                        <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                            <b>Subway crowd: Pull string tight</b> and slide the bag in front — secure & snug.
                        </p> 
                        <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                            <b>Light rain:</b> Keep walking — the <b>water-resistant</b> fabric shrugs it off.
                        </p>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Feature 7: Less shoulder strain (Image left, text right) - Dark stripe background -->
    <div class="w-full py-8 lg:py-12" style="background-color: #f3f3f3;">
        <div class="container mx-auto px-6 sm:px-8">
            <div class="w-full max-w-full mx-auto">
                <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                    <!-- Image -->
                    <div class="w-full lg:w-2/5">
                        <img src="{{feature7_image_url:https://picsum.photos/600/800?random=8}}"
                             class="w-full object-cover rounded-2xl shadow-lg">
                    </div>
                    <!-- Content -->
                    <div class="w-full lg:w-3/5 xl:px-12 lg:py-6">
                        <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                            {{feature7_heading:Proof It Matches Women 35+}}
                        </h3>
                        <p class="text-base lg:text-lg text-gray-700 mb-0 sm:mb-6 lg:mb-8 leading-relaxed">
                            Women 35+ remain the core daily handbag users (over <b>60%</b> “always carry”), so a <b>small-but-organized</b> bucket bag that speeds up daily routines hits the exact need: <b>function first, then style</b>.
                        </p>
                        <a href="{{feature7_cta_link:#}}"
                           class="bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 w-full sm:w-fit sm:min-w-60 text-lg hidden sm:inline-block text-center"
                        >
                            {{feature7_cta_text:See how it keeps you stylish & organized}}
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>    

</section>

<!-- Nordace Instagram Slider Section -->
<section class="py-16 lg:py-24" style="overflow: hidden;">
    <!-- Header content -->
    <div class="container mx-auto px-6 sm:px-8 mb-12 lg:mb-16">
        <div class="w-full max-w-full md:max-w-[{{instagram_content_max_width:800px}}] mx-auto text-left sm:text-center">
            <!-- Main heading -->
            <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                {{instagram_heading:Discover Nordace in Real Life}}
            </h2>
            
            <!-- Description -->
            <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                {{instagram_description:Curious about traveling with Nordace? These real stories from our community of travelers reveal the magic. Dive in and get inspired!}}
            </p>
        </div>
    </div>

    <!-- Instagram Slider -->
    <div class="relative instagram-slider-container group container mx-auto px-6 sm:px-8">
        <!-- Slider wrapper -->
        <div class="cursor-grab active:cursor-grabbing">
            <div class="instagram-slider flex gap-4 transition-transform duration-300 ease-out">
                
                <!-- Slide 1 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_1.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_0.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C92q_AeMrCz/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">welshmummysteph</span>
                    </a>
                </div>

                <!-- Slide 2 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_1.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C2b0rwJh952/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">pati.x.rose</span>
                    </a>
                </div>


                <!-- Slide 3 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_3.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/CyeVJXBSqsK/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">wishfornatureandtravel</span>
                    </a>
                </div>

                <!-- Slide 4 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_4_UPD.2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_3.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_cIhzTNgyt/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">stylebysheena</span>
                    </a>
                </div>

                <!-- Slide 5 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_5.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_5_poster2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C3pv-SrR6fg/?img_index=2" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rocknroamgirl</span>
                    </a>
                </div>

                <!-- Slide 6 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_6.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_4.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C8UN49TR5sD/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">travelloverfran33</span>
                    </a>
                </div>

                <!-- Slide 7 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_7.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_7_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_DEmyfAKgc/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">peepmybookshelf</span>
                    </a>
                </div>

                <!-- Slide 8 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_9.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_5.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C5VsKeRsmef/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">alittlepakistani</span>
                    </a>
                </div>

                <!-- Slide 9 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_10.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_10_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C57aS2WLFO5/?img_index=1" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rubyfrosttt</span>
                    </a>
                </div>

                <!-- Slide 10 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_11.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_6.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C4iMFvVLeKx/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">raisingmavericks</span>
                    </a>
                </div>

                <!-- Slide 11 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_12.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_7.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/Cx3FjfGRm9_/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">kateswanderlens</span>
                    </a>
                </div>

            </div>
        </div>

        <!-- Navigation arrows (hidden on mobile, visible on hover on desktop) -->
        <button class="hidden lg:block instagram-nav-prev absolute left-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </button>
        <button class="hidden lg:block instagram-nav-next absolute right-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliderContainer = document.querySelector('.instagram-slider-container');
    const slider = document.querySelector('.instagram-slider');
    const slides = document.querySelectorAll('.instagram-slide');
    const prevBtn = document.querySelector('.instagram-nav-prev');
    const nextBtn = document.querySelector('.instagram-nav-next');
    
    let isDragging = false;
    let startPos = 0;
    let currentTranslate = 0;
    let prevTranslate = 0;
    let currentIndex = 0;

    // Calculate maximum scroll index based on viewport
    function getMaxIndex() {
        const sliderWidth = slider.parentElement.offsetWidth;
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap

        // Get padding from the container
        const containerStyle = window.getComputedStyle(slider.parentElement);
        const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(containerStyle.paddingRight) || 0;
        const availableWidth = sliderWidth - paddingLeft - paddingRight;

        // Calculate how many slides fit in the viewport
        const slidesVisible = Math.floor((availableWidth + gap) / (slideWidth + gap));

        // Maximum index is total slides minus visible slides
        const maxIndex = Math.max(0, slides.length - slidesVisible);

        return maxIndex;
    }

    // Video hover play/pause
    slides.forEach(slide => {
        const video = slide.querySelector('video');
        if (video) {
            slide.addEventListener('mouseenter', () => {
                video.play();
            });
            slide.addEventListener('mouseleave', () => {
                video.pause();
            });
        }
    });
    
    // Drag functionality
    slider.addEventListener('mousedown', dragStart);
    slider.addEventListener('touchstart', dragStart);
    slider.addEventListener('mouseup', dragEnd);
    slider.addEventListener('touchend', dragEnd);
    slider.addEventListener('mousemove', drag);
    slider.addEventListener('touchmove', drag);
    slider.addEventListener('mouseleave', dragEnd);
    
    function dragStart(e) {
        isDragging = true;
        startPos = getPositionX(e);
        slider.style.cursor = 'grabbing';
    }
    
    function drag(e) {
        if (!isDragging) return;
        const currentPosition = getPositionX(e);
        currentTranslate = prevTranslate + currentPosition - startPos;
    }
    
    function dragEnd() {
        isDragging = false;
        slider.style.cursor = 'grab';

        const movedBy = currentTranslate - prevTranslate;
        const maxIndex = getMaxIndex();

        if (movedBy < -100 && currentIndex < maxIndex) {
            currentIndex += 1;
        }

        if (movedBy > 100 && currentIndex > 0) {
            currentIndex -= 1;
        }

        setPositionByIndex();
    }
    
    function getPositionX(e) {
        return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }
    
    function setPositionByIndex() {
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap
        const maxIndex = getMaxIndex();

        // Ensure currentIndex doesn't exceed maxIndex
        currentIndex = Math.min(currentIndex, maxIndex);

        currentTranslate = currentIndex * -(slideWidth + gap);
        prevTranslate = currentTranslate;
        slider.style.transform = `translateX(${currentTranslate}px)`;
    }
    
    // Navigation buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentIndex > 0) {
                currentIndex -= 1;
                setPositionByIndex();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            const maxIndex = getMaxIndex();
            if (currentIndex < maxIndex) {
                currentIndex += 1;
                setPositionByIndex();
            }
        });
    }

    // Recalculate on window resize
    window.addEventListener('resize', () => {
        setPositionByIndex();
    });
});
</script>

<!-- Nordace CTA Section -->
<section class="pb-40 sm:pb-16 lg:pb-24 pt-16 lg:pt-24" style="background-color: #f3f3f3;">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full md:max-w-[{{cta_content_max_width:800px}}] mx-auto">
            
            <!-- CTA Content -->
            <div class="text-left sm:text-center">
                <!-- Main heading -->
                <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                    {{cta_main_heading:Who It’s Perfect For}}
                </h2>
                
                <!-- Description -->
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>The multitasker mom</b> who needs both hands free.
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>The commuter</b> who hates digging at the turnstile.
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>The traveler</b> who wants light, secure, and quick.
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>The weekend walker</b> who carries just enough — not a tote
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    If you’ve ever said, “My bag eats my stuff,” this is the fix. <b>Siena Pro Bucket Bag</b> gives you <b>fast grabs</b>, <b>calm order</b>, and a look that works with everything — without the shoulder ache.
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-0 sm:mb-8 lg:mb-10 leading-relaxed">
                    <b>→ Find your new everyday favorite now!</b>
                </p>
                
                <!-- CTA Button - Hidden on mobile, visible on sm and up -->
                <div class="hidden sm:flex justify-start sm:justify-center">
                    <a href="{{cta_button_link:#}}" class="bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 w-full sm:w-fit sm:min-w-60 text-lg text-center inline-block">
                        {{cta_button_text:Buy Now}}
                    </a>
                </div>
            </div>
            
        </div>
    </div>
    
    <!-- Mobile Sticky Button - Visible only on mobile -->
    <div class="fixed bottom-0 left-0 right-0 sm:hidden bg-white border-t border-gray-200 p-5 z-50">
        <a href="{{cta_button_link:#}}" class="bg-black hover:bg-gray-800 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 w-full text-lg text-center block">
            {{cta_button_text:Buy Now}}
        </a>
    </div>
</section>

