<!-- FAQ Section -->
<section class="pb-40 sm:pb-16 lg:pb-24 pt-16 lg:pt-24" style="background-color: #f3f3f3;">
    <div class="container mx-auto px-6 sm:px-8">
        <div class="w-full max-w-full mx-auto">

            <!-- FAQ Header -->
            <div class="text-left sm:text-center mb-12 lg:mb-16">
                <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                    {{faq_heading:Frequently Asked Questions}}
                </h2>
            </div>

            <!-- FAQ Items -->
            <div class="max-w-4xl mx-auto space-y-6">

                <!-- FAQ Item 1 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <button class="faq-toggle w-full text-left p-6 lg:p-8 transition-all duration-300"
                            onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg lg:text-xl font-semibold text-gray-900 pr-4">
                                {{faq_question_1:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod?}}
                            </h3>
                            <svg class="faq-icon w-6 h-6 text-gray-600 transform transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                        <div class="p-6 lg:p-8 pt-0 lg:pt-0 text-base lg:text-lg text-gray-700 leading-relaxed">
                            {{faq_answer_1:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}}
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 2 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <button class="faq-toggle w-full text-left p-6 lg:p-8 transition-all duration-300"
                            onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg lg:text-xl font-semibold text-gray-900 pr-4">
                                {{faq_question_2:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod?}}
                            </h3>
                            <svg class="faq-icon w-6 h-6 text-gray-600 transform transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                        <div class="p-6 lg:p-8 pt-0 lg:pt-0 text-base lg:text-lg text-gray-700 leading-relaxed">
                            {{faq_answer_2:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}}
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 3 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <button class="faq-toggle w-full text-left p-6 lg:p-8 transition-all duration-300"
                            onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg lg:text-xl font-semibold text-gray-900 pr-4">
                                {{faq_question_3:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod?}}
                            </h3>
                            <svg class="faq-icon w-6 h-6 text-gray-600 transform transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                        <div class="p-6 lg:p-8 pt-0 lg:pt-0 text-base lg:text-lg text-gray-700 leading-relaxed">
                            {{faq_answer_3:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}}
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 4 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <button class="faq-toggle w-full text-left p-6 lg:p-8 transition-all duration-300"
                            onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg lg:text-xl font-semibold text-gray-900 pr-4">
                                {{faq_question_4:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod?}}
                            </h3>
                            <svg class="faq-icon w-6 h-6 text-gray-600 transform transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                        <div class="p-6 lg:p-8 pt-0 lg:pt-0 text-base lg:text-lg text-gray-700 leading-relaxed">
                            {{faq_answer_4:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}}
                        </div>
                    </div>
                </div>

                <!-- FAQ Item 5 -->
                <div class="bg-white rounded-2xl shadow-lg overflow-hidden">
                    <button class="faq-toggle w-full text-left p-6 lg:p-8 transition-all duration-300"
                            onclick="toggleFAQ(this)">
                        <div class="flex justify-between items-center">
                            <h3 class="text-lg lg:text-xl font-semibold text-gray-900 pr-4">
                                {{faq_question_5:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod?}}
                            </h3>
                            <svg class="faq-icon w-6 h-6 text-gray-600 transform transition-transform duration-300"
                                 fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M19 9l-7 7-7-7"/>
                            </svg>
                        </div>
                    </button>
                    <div class="faq-content max-h-0 overflow-hidden transition-all duration-300 ease-out">
                        <div class="p-6 lg:p-8 pt-0 lg:pt-0 text-base lg:text-lg text-gray-700 leading-relaxed">
                            {{faq_answer_5:Lorem ipsum dolor sit amet, consectetur adipiscing elit, sed do eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut enim ad minim veniam, quis nostrud exercitation ullamco laboris nisi ut aliquip ex ea commodo consequat.}}
                        </div>
                    </div>
                </div>

            </div>

            <!-- FAQ CTA Button -->
            <div class="hidden sm:flex justify-start sm:justify-center mt-12 lg:mt-16">
                <a href="{{faq_cta_link:#}}"
                   class="bg-black hover:bg-gray-800 text-white font-semibold py-4 px-8 rounded-lg transition-all duration-300 text-lg inline-block">
                    {{faq_cta_text:Buy Now}}
                </a>
            </div>

        </div>
    </div>

    <!-- Mobile Sticky Button - Visible only on mobile -->
    <div class="fixed bottom-0 left-0 right-0 sm:hidden bg-white border-t border-gray-200 p-5 z-50">
        <a href="{{cta_button_link:#}}" class="bg-black hover:bg-gray-800 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 w-full text-lg text-center block">
            {{cta_button_text:Buy Now}}
        </a>
    </div>

</section>

<script>
function toggleFAQ(button) {
    const content = button.nextElementSibling;
    const icon = button.querySelector('.faq-icon');
    const isOpen = content.style.maxHeight && content.style.maxHeight !== '0px';

    // Close all other FAQ items
    document.querySelectorAll('.faq-content').forEach(item => {
        if (item !== content) {
            item.style.maxHeight = '0px';
            item.previousElementSibling.querySelector('.faq-icon').style.transform = 'rotate(0deg)';
        }
    });

    // Toggle current FAQ item
    if (isOpen) {
        content.style.maxHeight = '0px';
        icon.style.transform = 'rotate(0deg)';
    } else {
        content.style.maxHeight = content.scrollHeight + 'px';
        icon.style.transform = 'rotate(180deg)';
    }
}
</script>




