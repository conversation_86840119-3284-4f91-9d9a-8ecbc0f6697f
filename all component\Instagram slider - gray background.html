<!-- Nordace Instagram Slider Section -->
<section class="py-16 lg:py-24" style="background-color: #f3f3f3; overflow: hidden;">
    <!-- Header content -->
    <div class="container mx-auto px-6 sm:px-8 mb-12 lg:mb-16">
        <div class="w-full max-w-full md:max-w-[{{instagram_content_max_width:800px}}] mx-auto text-left sm:text-center">
            <!-- Main heading -->
            <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                {{instagram_heading:Discover Nordace in Real Life}}
            </h2>
            
            <!-- Description -->
            <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                {{instagram_description:Curious about traveling with Nordace? These real stories from our community of travelers reveal the magic. Dive in and get inspired!}}
            </p>
        </div>
    </div>

    <!-- Instagram Slider -->
    <div class="relative instagram-slider-container group container mx-auto px-6 sm:px-8">
        <!-- Slider wrapper -->
        <div class="cursor-grab active:cursor-grabbing">
            <div class="instagram-slider flex gap-4 transition-transform duration-300 ease-out">
                
                <!-- Slide 1 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_1.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_0.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C92q_AeMrCz/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">welshmummysteph</span>
                    </a>
                </div>

                <!-- Slide 2 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_1.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C2b0rwJh952/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">pati.x.rose</span>
                    </a>
                </div>


                <!-- Slide 3 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_3.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/CyeVJXBSqsK/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">wishfornatureandtravel</span>
                    </a>
                </div>

                <!-- Slide 4 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_4_UPD.2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_3.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_cIhzTNgyt/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">stylebysheena</span>
                    </a>
                </div>

                <!-- Slide 5 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_5.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_5_poster2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C3pv-SrR6fg/?img_index=2" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rocknroamgirl</span>
                    </a>
                </div>

                <!-- Slide 6 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_6.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_4.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C8UN49TR5sD/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">travelloverfran33</span>
                    </a>
                </div>

                <!-- Slide 7 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_7.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_7_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_DEmyfAKgc/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">peepmybookshelf</span>
                    </a>
                </div>

                <!-- Slide 8 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_9.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_5.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C5VsKeRsmef/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">alittlepakistani</span>
                    </a>
                </div>

                <!-- Slide 9 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_10.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_10_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C57aS2WLFO5/?img_index=1" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rubyfrosttt</span>
                    </a>
                </div>

                <!-- Slide 10 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_11.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_6.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C4iMFvVLeKx/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">raisingmavericks</span>
                    </a>
                </div>

                <!-- Slide 11 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_12.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_7.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/Cx3FjfGRm9_/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">kateswanderlens</span>
                    </a>
                </div>

            </div>
        </div>

        <!-- Navigation arrows (hidden on mobile, visible on hover on desktop) -->
        <button class="hidden lg:block instagram-nav-prev absolute left-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </button>
        <button class="hidden lg:block instagram-nav-next absolute right-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliderContainer = document.querySelector('.instagram-slider-container');
    const slider = document.querySelector('.instagram-slider');
    const slides = document.querySelectorAll('.instagram-slide');
    const prevBtn = document.querySelector('.instagram-nav-prev');
    const nextBtn = document.querySelector('.instagram-nav-next');
    
    let isDragging = false;
    let startPos = 0;
    let currentTranslate = 0;
    let prevTranslate = 0;
    let currentIndex = 0;

    // Calculate maximum scroll index based on viewport
    function getMaxIndex() {
        const sliderWidth = slider.parentElement.offsetWidth;
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap

        // Get padding from the container
        const containerStyle = window.getComputedStyle(slider.parentElement);
        const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(containerStyle.paddingRight) || 0;
        const availableWidth = sliderWidth - paddingLeft - paddingRight;

        // Calculate how many slides fit in the viewport
        const slidesVisible = Math.floor((availableWidth + gap) / (slideWidth + gap));

        // Maximum index is total slides minus visible slides
        const maxIndex = Math.max(0, slides.length - slidesVisible);

        return maxIndex;
    }

    // Video hover play/pause
    slides.forEach(slide => {
        const video = slide.querySelector('video');
        if (video) {
            slide.addEventListener('mouseenter', () => {
                video.play();
            });
            slide.addEventListener('mouseleave', () => {
                video.pause();
            });
        }
    });
    
    // Drag functionality
    slider.addEventListener('mousedown', dragStart);
    slider.addEventListener('touchstart', dragStart);
    slider.addEventListener('mouseup', dragEnd);
    slider.addEventListener('touchend', dragEnd);
    slider.addEventListener('mousemove', drag);
    slider.addEventListener('touchmove', drag);
    slider.addEventListener('mouseleave', dragEnd);
    
    function dragStart(e) {
        isDragging = true;
        startPos = getPositionX(e);
        slider.style.cursor = 'grabbing';
    }
    
    function drag(e) {
        if (!isDragging) return;
        const currentPosition = getPositionX(e);
        currentTranslate = prevTranslate + currentPosition - startPos;
    }
    
    function dragEnd() {
        isDragging = false;
        slider.style.cursor = 'grab';

        const movedBy = currentTranslate - prevTranslate;
        const maxIndex = getMaxIndex();

        if (movedBy < -100 && currentIndex < maxIndex) {
            currentIndex += 1;
        }

        if (movedBy > 100 && currentIndex > 0) {
            currentIndex -= 1;
        }

        setPositionByIndex();
    }
    
    function getPositionX(e) {
        return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }
    
    function setPositionByIndex() {
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap
        const maxIndex = getMaxIndex();

        // Ensure currentIndex doesn't exceed maxIndex
        currentIndex = Math.min(currentIndex, maxIndex);

        currentTranslate = currentIndex * -(slideWidth + gap);
        prevTranslate = currentTranslate;
        slider.style.transform = `translateX(${currentTranslate}px)`;
    }
    
    // Navigation buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentIndex > 0) {
                currentIndex -= 1;
                setPositionByIndex();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            const maxIndex = getMaxIndex();
            if (currentIndex < maxIndex) {
                currentIndex += 1;
                setPositionByIndex();
            }
        });
    }

    // Recalculate on window resize
    window.addEventListener('resize', () => {
        setPositionByIndex();
    });
});
</script>
