<!-- Nordace Article Section -->
<section class="pb-0 pt-16 lg:pt-24">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full mx-auto">
            
            <!-- Article Content -->
            <div class="text-left">
                <!-- Main heading -->
                <h1 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-8 lg:mb-12 leading-tight xl:leading-tight">
                    {{article_heading:8 Reasons This Boston Bag Is The Best Anti‑Theft Bag (for women who have zero time for pickpockets)}}
                </h1>
                
                <!-- Author info -->
                <div class="flex items-center gap-4 mb-4 lg:mb-5">
                    <!-- Author avatar -->
                    <img src="{{author_avatar_url:https://picsum.photos/50/50?random=11}}" 
                         class="w-12 h-12 rounded-full object-cover">
                    
                    <!-- Author details -->
                    <div>
                        <p class="text-sm font-medium text-gray-900">
                            By {{author_name:<PERSON>}}
                        </p>
                        <p class="text-sm text-gray-600">
                            Last Updated {{last_updated:Last Updated Oct 15, 2025}}
                        </p>
                    </div>
                </div>
                
                <!-- Quote section -->
                <div class="border-l-4 border-gray-900 pl-6 mb-8 py-3 lg:mb-10 bg-orange-50">
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        Luxury? Yes. Easy to get robbed? <b>Never</b>.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 italic leading-relaxed">
                        If you’ve ever done the “door-key dance,” fished your passport from a black hole, or kept a death‑grip on your tote in a crowd—this is your sign. Meet the <b>Nordace Siena Riviera Boston Bag</b>: small footprint, big peace of mind.
                    </p>
                </div>
                
                <!-- TLDR section -->
                <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                    <span class="font-bold text-gray-900">{{tldr_label:Why trust this list?}} </span> 
                    <span>{{tldr_content:If you want a bag that looks chic and quietly blocks the three fastest ways people lose stuff—open tops, messy interiors, and slow fingers—this is it.}} </span>
                    <span class="text-xl">{{tldr_emoji:}}</span>
                </p>
            </div>
            
        </div>
    </div>
</section>

<!-- Nordace Features Section -->
<section class="py-16 lg:py-24">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full mx-auto space-y-16 lg:space-y-24">

            <!-- Feature 1: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature1_image_url:https://picsum.photos/800/800?random=1}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature1_heading:1. A ZIP‑TOP FORTRESS (a thief’s worst enemy)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> A full‑length zipper and a structured opening.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> Most street theft is “opportunity theft.” Open totes = easy hands. A tight, zipped mouth turns a 2‑second grab into an awkward struggle. Thieves hate awkward.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Real life:</b> On the metro, zip it shut and keep the zipper toward your front. You just removed 90% of the easy chances.
                    </p>
                </div>
            </div>

            <!-- Feature 2: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature2_image_url:https://picsum.photos/800/800?random=2}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature2_heading:2. A “PASSPORT SAFE” BACK POCKET (fast for you, slow for them)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> A snug, body‑side pocket sized for passports and travel docs.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> Thieves target front‑facing openings. A back pocket pressed against you is out of their line of attack. Your document sits low, flat, and calm.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Bonus:</b> No more flashing your passport every time you reach for lip balm.
                    </p>
                </div>
            </div>

            <!-- Feature 3: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature3_image_url:https://picsum.photos/800/800?random=3}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature3_heading:3. RFID‑Blocking Card Slots (because skimmers exist)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> Built‑in RFID‑shielded slots for your most sensitive cards.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> RFID skimming isn’t common everywhere, but it’s cheap to attempt in crowds. Blocking tech is a tiny, invisible safety net that costs you nothing in effort.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Translation:</b> Tap‑to‑pay in peace. Airport lines without that “what if?” itch.
                    </p>
                </div>
            </div>

            <!-- Feature 4: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature4_image_url:https://picsum.photos/800/800?random=4}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature4_heading:4. Instant‑Grab Keys Leash (the end of the door‑key dance)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> A retractable key leash clipped inside.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> The longer you rummage, the more distracted and vulnerable you are. One pull—click—keys in hand, bag still closed. You look organized. You feel safe.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Side effect:</b> You arrive home like a ninja, not a raccoon in a trash bin.
                    </p>
                </div>
            </div>

            <!-- Feature 5: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature5_image_url:https://picsum.photos/800/800?random=5}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature5_heading:5. Hidden Safety Pockets (stash like a pro)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> Interior hideaways for emergency cash, rings, or a spare card.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Why it works:</b> Layered storage = layered security. If someone ever gets past the main zip (rare), your true valuables still play hide‑and‑seek. You win.
                    </p>
                </div>
            </div>      
            
            <!-- Feature 6: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature6_image_url:https://picsum.photos/800/800?random=6}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature6_heading:6. Looks Small, Fits All (less bulk, less chaos)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> A compact, structured shape that packs vertically.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> A tidy interior means less time with your bag gaping open while you dig for glasses/chargers/receipts. Fewer seconds = fewer opportunities.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Grown‑woman capacity checklist:</b> phone, wallet, passport, sunnies, meds, compact, charger, tissues, and a small umbrella. Yep.
                    </p>
                </div>
            </div>  
            
            <!-- Feature 7: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature7_image_url:https://picsum.photos/800/800?random=7}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature7_heading:7. Top‑Handle to Crossbody (carry smart, not heavy)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> Elegant handles + detachable strap to wear crossbody.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>Why it works:</b> Crossbody keeps the bag anchored to you and your hands free—perfect in markets, stations, and festival crowds. When you want chic, grab the handles. Same bag, two “moods.”
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Safety tip:</b> Wear crossbody with the zipper pull toward your front. Chef’s kiss.
                    </p>
                </div>
            </div> 

            <!-- Feature 8: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature8_image_url:https://picsum.photos/800/800?random=8}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature8_heading:8. Lock‑Friendly Hardware & Tough Build (slow them way down)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        <b>What you get:</b> Solid hardware and zipper pulls you can clip together or add a tiny travel lock to.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        <b>Why it works:</b> Thieves choose the fastest target. If your bag takes extra steps, they move on. The sturdy, wipe‑clean fabric also shrugs off drizzle and travel grime—less fuss, more focus.
                    </p>
                </div>
            </div>
            
            <!-- Feature 9: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature9_image_url:https://picsum.photos/800/800?random=9}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature9_heading:9. But is any bag 100% theft‑proof?}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        No. <b>Habits + smart design</b> = peace of mind. This bag gives you the design. Here are the habits:
                    </p>
                    <ul class="list-disc list-inside text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 pl-3 sm:pl-6 leading-relaxed">
                        <li>Zip it. Always.</li>
                        <li>Keep the passport in the back pocket.</li>
                        <li>Wear crossbody in crowds.</li>
                        <li>Use the key leash so you never “rummage with your bag open.”</li>
                    </ul> 
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        Do that, and you’ll feel that instant “I’ve got this” confidence women 35+ wear better than lipstick.
                    </p>
                </div>
            </div>  

            <!-- Feature 10: (Image left, text right) -->
            <div class="flex flex-col lg:flex-row gap-8 lg:gap-12 items-center">
                <!-- Image -->
                <div class="w-full lg:w-1/2">
                    <img src="{{feature10_image_url:https://picsum.photos/800/800?random=10}}"
                         class="w-full object-cover rounded-2xl shadow-lg">
                </div>
                <!-- Content -->
                <div class="w-full lg:w-1/2">
                    <h3 class="text-2xl lg:text-3xl font-bold text-gray-900 mb-4 lg:mb-6 leading-tight">
                        {{feature10_heading:10. The 30‑Second Setup (before your trip or daily run)}}
                    </h3>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        Slide passport + boarding pass into the back pocket.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        Cards into the RFID slots, cash in the hidden pocket.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                        Clip keys to the leash.
                    </p>
                    <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                        Crossbody strap on, zipper facing front.<br>
                        Done. You’re the calmest person in line.
                    </p>
                </div>
            </div>                     

        </div>
    </div>
</section>

<!-- Nordace Instagram Slider Section -->
<section class="py-16 lg:py-24" style="background-color: #f3f3f3; overflow: hidden;">
    <!-- Header content -->
    <div class="container mx-auto px-6 sm:px-8 mb-12 lg:mb-16">
        <div class="w-full max-w-full md:max-w-[{{instagram_content_max_width:800px}}] mx-auto text-left sm:text-center">
            <!-- Main heading -->
            <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                {{instagram_heading:Discover Nordace in Real Life}}
            </h2>
            
            <!-- Description -->
            <p class="text-base lg:text-lg text-gray-700 leading-relaxed">
                {{instagram_description:Curious about traveling with Nordace? These real stories from our community of travelers reveal the magic. Dive in and get inspired!}}
            </p>
        </div>
    </div>

    <!-- Instagram Slider -->
    <div class="relative instagram-slider-container group container mx-auto px-6 sm:px-8">
        <!-- Slider wrapper -->
        <div class="cursor-grab active:cursor-grabbing">
            <div class="instagram-slider flex gap-4 transition-transform duration-300 ease-out">
                
                <!-- Slide 1 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_1.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_0.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C92q_AeMrCz/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">welshmummysteph</span>
                    </a>
                </div>

                <!-- Slide 2 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <!-- Video background -->
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_1.jpg">
                    </video>
                    
                    <!-- Instagram overlay -->
                    <a href="https://www.instagram.com/p/C2b0rwJh952/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <!-- Instagram icon -->
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">pati.x.rose</span>
                    </a>
                </div>


                <!-- Slide 3 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_3.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/CyeVJXBSqsK/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">wishfornatureandtravel</span>
                    </a>
                </div>

                <!-- Slide 4 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_4_UPD.2.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_3.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_cIhzTNgyt/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">stylebysheena</span>
                    </a>
                </div>

                <!-- Slide 5 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_5.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_5_poster2.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C3pv-SrR6fg/?img_index=2" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rocknroamgirl</span>
                    </a>
                </div>

                <!-- Slide 6 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_6.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_4.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C8UN49TR5sD/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">travelloverfran33</span>
                    </a>
                </div>

                <!-- Slide 7 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_7.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_7_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C_DEmyfAKgc/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">peepmybookshelf</span>
                    </a>
                </div>

                <!-- Slide 8 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_9.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_5.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C5VsKeRsmef/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">alittlepakistani</span>
                    </a>
                </div>

                <!-- Slide 9 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_10.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2024/11/Reel_10_poster.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C57aS2WLFO5/?img_index=1" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">rubyfrosttt</span>
                    </a>
                </div>

                <!-- Slide 10 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_11.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_6.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/C4iMFvVLeKx/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">raisingmavericks</span>
                    </a>
                </div>

                <!-- Slide 11 -->
                <div class="instagram-slide flex-shrink-0 w-[85%] sm:w-[calc(33.333%-0.66rem)] lg:w-[calc(25%-0.75rem)] relative rounded-2xl overflow-hidden aspect-[2/3] bg-gray-200">
                    <video 
                        class="absolute inset-0 w-full h-full object-cover"
                        src="https://nordace.com/wp-content/uploads/2024/11/Reel_12.mp4"
                        loop
                        muted
                        playsinline
                        poster="https://nordace.com/wp-content/uploads/2025/10/videoframe_7.jpg">
                    </video>
                    <a href="https://www.instagram.com/p/Cx3FjfGRm9_/" 
                       target="_blank"
                       class="absolute bottom-0 left-0 right-0 p-4 bg-gradient-to-t from-black/70 to-transparent flex items-center gap-2 hover:from-black/80 transition-all">
                        <svg class="w-6 h-6 text-white" fill="currentColor" viewBox="0 0 24 24">
                            <path d="M12 2.163c3.204 0 3.584.012 4.85.07 3.252.148 4.771 1.691 4.919 4.919.058 1.265.069 1.645.069 4.849 0 3.205-.012 3.584-.069 4.849-.149 3.225-1.664 4.771-4.919 4.919-1.266.058-1.644.07-4.85.07-3.204 0-3.584-.012-4.849-.07-3.26-.149-4.771-1.699-4.919-4.92-.058-1.265-.07-1.644-.07-4.849 0-3.204.013-3.583.07-4.849.149-3.227 1.664-4.771 4.919-4.919 1.266-.057 1.645-.069 4.849-.069zm0-2.163c-3.259 0-3.667.014-4.947.072-4.358.2-6.78 2.618-6.98 6.98-.059 1.281-.073 1.689-.073 4.948 0 3.259.014 3.668.072 4.948.2 4.358 2.618 6.78 6.98 6.98 1.281.058 1.689.072 4.948.072 3.259 0 3.668-.014 4.948-.072 4.354-.2 6.782-2.618 6.979-6.98.059-1.28.073-1.689.073-4.948 0-3.259-.014-3.667-.072-4.947-.196-4.354-2.617-6.78-6.979-6.98-1.281-.059-1.69-.073-4.949-.073zm0 5.838c-3.403 0-6.162 2.759-6.162 6.162s2.759 6.163 6.162 6.163 6.162-2.759 6.162-6.163c0-3.403-2.759-6.162-6.162-6.162zm0 10.162c-2.209 0-4-1.79-4-4 0-2.209 1.791-4 4-4s4 1.791 4 4c0 2.21-1.791 4-4 4zm6.406-11.845c-.796 0-1.441.645-1.441 1.44s.645 1.44 1.441 1.44c.795 0 1.439-.645 1.439-1.44s-.644-1.44-1.439-1.44z"/>
                        </svg>
                        <span class="text-white text-sm font-medium">kateswanderlens</span>
                    </a>
                </div>

            </div>
        </div>

        <!-- Navigation arrows (hidden on mobile, visible on hover on desktop) -->
        <button class="hidden lg:block instagram-nav-prev absolute left-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 19l-7-7 7-7"/>
            </svg>
        </button>
        <button class="hidden lg:block instagram-nav-next absolute right-12 top-1/2 -translate-y-1/2 bg-white/90 hover:bg-white text-gray-900 rounded-full p-3 shadow-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 z-10">
            <svg class="w-6 h-6" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5l7 7-7 7"/>
            </svg>
        </button>
    </div>
</section>

<script>
document.addEventListener('DOMContentLoaded', function() {
    const sliderContainer = document.querySelector('.instagram-slider-container');
    const slider = document.querySelector('.instagram-slider');
    const slides = document.querySelectorAll('.instagram-slide');
    const prevBtn = document.querySelector('.instagram-nav-prev');
    const nextBtn = document.querySelector('.instagram-nav-next');
    
    let isDragging = false;
    let startPos = 0;
    let currentTranslate = 0;
    let prevTranslate = 0;
    let currentIndex = 0;

    // Calculate maximum scroll index based on viewport
    function getMaxIndex() {
        const sliderWidth = slider.parentElement.offsetWidth;
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap

        // Get padding from the container
        const containerStyle = window.getComputedStyle(slider.parentElement);
        const paddingLeft = parseFloat(containerStyle.paddingLeft) || 0;
        const paddingRight = parseFloat(containerStyle.paddingRight) || 0;
        const availableWidth = sliderWidth - paddingLeft - paddingRight;

        // Calculate how many slides fit in the viewport
        const slidesVisible = Math.floor((availableWidth + gap) / (slideWidth + gap));

        // Maximum index is total slides minus visible slides
        const maxIndex = Math.max(0, slides.length - slidesVisible);

        return maxIndex;
    }

    // Video hover play/pause
    slides.forEach(slide => {
        const video = slide.querySelector('video');
        if (video) {
            slide.addEventListener('mouseenter', () => {
                video.play();
            });
            slide.addEventListener('mouseleave', () => {
                video.pause();
            });
        }
    });
    
    // Drag functionality
    slider.addEventListener('mousedown', dragStart);
    slider.addEventListener('touchstart', dragStart);
    slider.addEventListener('mouseup', dragEnd);
    slider.addEventListener('touchend', dragEnd);
    slider.addEventListener('mousemove', drag);
    slider.addEventListener('touchmove', drag);
    slider.addEventListener('mouseleave', dragEnd);
    
    function dragStart(e) {
        isDragging = true;
        startPos = getPositionX(e);
        slider.style.cursor = 'grabbing';
    }
    
    function drag(e) {
        if (!isDragging) return;
        const currentPosition = getPositionX(e);
        currentTranslate = prevTranslate + currentPosition - startPos;
    }
    
    function dragEnd() {
        isDragging = false;
        slider.style.cursor = 'grab';

        const movedBy = currentTranslate - prevTranslate;
        const maxIndex = getMaxIndex();

        if (movedBy < -100 && currentIndex < maxIndex) {
            currentIndex += 1;
        }

        if (movedBy > 100 && currentIndex > 0) {
            currentIndex -= 1;
        }

        setPositionByIndex();
    }
    
    function getPositionX(e) {
        return e.type.includes('mouse') ? e.pageX : e.touches[0].clientX;
    }
    
    function setPositionByIndex() {
        const slideWidth = slides[0].offsetWidth;
        const gap = 16; // 1rem gap
        const maxIndex = getMaxIndex();

        // Ensure currentIndex doesn't exceed maxIndex
        currentIndex = Math.min(currentIndex, maxIndex);

        currentTranslate = currentIndex * -(slideWidth + gap);
        prevTranslate = currentTranslate;
        slider.style.transform = `translateX(${currentTranslate}px)`;
    }
    
    // Navigation buttons
    if (prevBtn) {
        prevBtn.addEventListener('click', () => {
            if (currentIndex > 0) {
                currentIndex -= 1;
                setPositionByIndex();
            }
        });
    }
    
    if (nextBtn) {
        nextBtn.addEventListener('click', () => {
            const maxIndex = getMaxIndex();
            if (currentIndex < maxIndex) {
                currentIndex += 1;
                setPositionByIndex();
            }
        });
    }

    // Recalculate on window resize
    window.addEventListener('resize', () => {
        setPositionByIndex();
    });
});
</script>

<!-- Nordace CTA Section -->
<section class="pb-40 sm:pb-16 lg:pb-24 pt-16 lg:pt-24">
    <!-- Main content container -->
    <div class="container mx-auto px-6 sm:px-8">
        <!-- Content wrapper with controllable width -->
        <div class="w-full max-w-full md:max-w-[{{cta_content_max_width:800px}}] mx-auto">
            
            <!-- CTA Content -->
            <div class="text-left sm:text-center">
                <!-- Main heading -->
                <h2 class="text-3xl lg:text-4xl xl:text-5xl font-bold text-gray-900 mb-6 lg:mb-8 leading-tight">
                    {{cta_main_heading:Who will love this anti-theft bag?}}
                </h2>
                              
                <!-- Description -->
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>Solo travelers</b> and moms who manage everyone’s documents.
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-2 sm:mb-3 leading-relaxed">
                    <b>City commuters</b> who want luxury looks without the “open‑tote panic.”
                </p>
                <p class="text-base lg:text-lg text-gray-700 mb-0 sm:mb-8 lg:mb-10 leading-relaxed">
                    <b>Minimalists</b> who like small bags that still carry real life.
                </p>
                
                <!-- CTA Button - Hidden on mobile, visible on sm and up -->
                <div class="hidden sm:flex justify-start sm:justify-center">
                    <a href="{{cta_button_link:https://nordace.com/product/siena-riviera-boston-bag/}}" class="bg-black hover:bg-gray-800 text-white font-semibold py-3 px-6 rounded-lg transition-all duration-300 w-full sm:w-fit sm:min-w-60 text-lg text-center inline-block">
                        {{cta_button_text:Level Up Your Security & Chic Now}}
                    </a>
                </div>
            </div>
            
        </div>
    </div>
    
    <!-- Mobile Sticky Button - Visible only on mobile -->
    <div class="fixed bottom-0 left-0 right-0 sm:hidden bg-white border-t border-gray-200 p-5 z-50">
        <a href="{{cta_button_link:https://nordace.com/product/siena-riviera-boston-bag/}}" class="bg-black hover:bg-gray-800 text-white font-semibold py-4 px-6 rounded-lg transition-all duration-300 w-full text-lg text-center block">
            {{cta_button_text:Level Up Your Security & Chic Now}}
        </a>
    </div>
</section>
